<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Domains\WhatsApp\ChangeValueMessage;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;

class ProcessWebhookMessage
{
    private ChatBotService $chatBotService;

    public function __construct(ChatBotService $chatBotService)
    {
        $this->chatBotService = $chatBotService;
    }

    /**
     * Process webhook message using ChatBot service
     *
     * @param ChangeValue $changeValue
     * @param Organization $organization
     * @param PhoneNumber $phoneNumber
     * @return array
     */
    public function perform(ChangeValue $changeValue, Organization $organization, PhoneNumber $phoneNumber): array
    {
        try {
            if (!$changeValue->hasMessages()) {
                return [
                    'success' => false,
                    'error' => 'No messages found in change value',
                    'processed' => 0
                ];
            }

            if (!$phoneNumber->shouldProcessChatBot()) {
                return [
                    'success' => false,
                    'error' => 'ChatBot processing disabled for this phone number',
                    'reason' => 'Phone number does not meet ChatBot activation criteria',
                    'processed' => 0,
                    'organization_id' => $organization->id,
                    'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                    'phone_number_checks' => [
                        'is_active' => $phoneNumber->is_active,
                        'is_chatbot_activated' => $phoneNumber->is_chatbot_activated,
                        'has_flow' => $phoneNumber->flow_id !== null
                    ]
                ];
            }

            $results = [];
            $processedCount = 0;

            // Get incoming messages (filter out outgoing)
            $incomingMessages = $changeValue->getIncomingMessages($phoneNumber);

            // Process each incoming message
            foreach ($incomingMessages as $messageData) {
                $message = new ChangeValueMessage($messageData);

                // Prepare webhook data for ChatBot service
                $webhookData = [
                    'message' => $message->toArray(),
                    'metadata' => $changeValue->metadata,
                    'contacts' => $changeValue->contacts,
                    'organization' => $organization,
                    'phone_number' => $phoneNumber
                ];

                // Process through ChatBot service
                $result = $this->chatBotService->processWebhook($webhookData);

                if ($result['success'] ?? false) {
                    $results[] = $result;
                    $processedCount++;
                }
            }

            return [
                'success' => true,
                'type' => 'message',
                'processed' => $processedCount,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                'results' => $results
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => 'message',
                'processed' => 0,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id
            ];
        }
    }


}
