<?php

namespace Tests\Feature\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhook;
use App\UseCases\WhatsAppWebhookLog\LogWebhookEvent;
use App\UseCases\ChatBot\ExchangedMessage\SaveExchangedMessagesFromWebhook;
use Tests\TestCase;
use Mockery;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProcessWebhookTest extends TestCase
{
    use RefreshDatabase;
    private ProcessWebhook $processWebhook;
    private $mockLogWebhookEvent;
    private $mockSaveExchangedMessages;

    protected function setUp(): void
    {
        parent::setUp();

        $this->processWebhook = new ProcessWebhook();

        // Mock dependencies
        $this->mockLogWebhookEvent = Mockery::mock(LogWebhookEvent::class);
        $this->mockSaveExchangedMessages = Mockery::mock(SaveExchangedMessagesFromWebhook::class);

        // Bind mocks to container
        $this->app->instance(LogWebhookEvent::class, $this->mockLogWebhookEvent);
        $this->app->instance(SaveExchangedMessagesFromWebhook::class, $this->mockSaveExchangedMessages);
    }

    public function test_perform_saves_exchanged_messages_when_has_messages()
    {
        // Arrange
        $organizationModel = \App\Models\Organization::factory()->create();
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);

        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);

        $changeValueData = [
            'messaging_product' => 'whatsapp',
            'metadata' => ['display_phone_number' => '1234567890'],
            'contacts' => [['profile' => ['name' => 'Test User']]],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '5511999999999',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Mock webhook log
        $mockWebhookLog = new \App\Domains\WhatsAppWebhookLog(
            123, // id
            $organization->id, // organization_id
            $phoneNumber->whatsapp_phone_number_id, // phone_number_id
            'message', // event_type
            [], // webhook_payload
            null, // processed_at
            'pending', // processing_status
            null, // error_message
            now(), // created_at
            now()  // updated_at
        );
        $this->mockLogWebhookEvent
            ->shouldReceive('perform')
            ->once()
            ->andReturn($mockWebhookLog);

        // Mock save exchanged messages
        $this->mockSaveExchangedMessages
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, 123)
            ->andReturn(['success' => true, 'processed' => 1]);

        // Mock ProcessWebhookMessage
        $mockProcessWebhookMessage = Mockery::mock(\App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhookMessage::class);
        $mockProcessWebhookMessage
            ->shouldReceive('perform')
            ->once()
            ->andReturn(['success' => true, 'type' => 'message']);

        $this->app->instance(\App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhookMessage::class, $mockProcessWebhookMessage);

        // Act
        $result = $this->processWebhook->perform($changeValue, $organization, $phoneNumber);

        // Assert
        $this->assertNotNull($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('message', $result['type']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
